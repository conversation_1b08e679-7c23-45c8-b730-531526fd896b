describe('Quiz Edit Question', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  it('TC-04-02-01 edit question', () => {
    cy.clickEdit();
    cy.get(
      ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > :nth-child(1) > [data-v-f912b43d=""] > .input-wrapper > .editable-div',
    )
      .clear()
      .type('question1');
    cy.get('.q-pa-md').click({ force: true });
    cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
    cy.clickEdit();
    cy.get(
      ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > :nth-child(1) > [data-v-f912b43d=""] > .input-wrapper > .editable-div',
    ).should('contain', 'question1');
  });

  it('TC-04-02-02 edit option', () => {
    cy.clickEdit();
    cy.get(
      ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > .q-ml-md > .q-mb-sm > :nth-child(3) > .q-field > .q-field__inner > .q-field__control > .q-field__control-container > [data-cy="option-text-input-0"]',
    )
      .clear()
      .type('option1');
    cy.get('.q-pa-md').click({ force: true });
    cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
    cy.clickEdit();
    cy.get(
      ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > .q-ml-md > .q-mb-sm > :nth-child(3) > .q-field > .q-field__inner > .q-field__control > .q-field__control-container > [data-cy="option-text-input-0"]',
    ).should('have.value', 'option1');
  });
});
