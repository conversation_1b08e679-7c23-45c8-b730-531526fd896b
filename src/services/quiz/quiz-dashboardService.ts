/* eslint-disable @typescript-eslint/no-unused-vars */
import { api as axios } from 'src/boot/axios';
import { Notify } from 'quasar';
import type {
  AssessmentMeta,
  ChartData,
  QuestionResponseData,
  ParticipantData,
  ParticipantDetails,
} from 'src/types/quiz-dashboard';
import type { DataParams, DataResponse } from 'src/types/data';

export class QuizDashboardService {
  private basePath = '/assessments/dashboard/quiz';

  async getDashboardMeta(assessmentId: number): Promise<AssessmentMeta> {
    try {
      const response = await axios.get<AssessmentMeta>(`${this.basePath}/${assessmentId}`);
      return response.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถโหลดข้อมูลเมตาได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Fetch dashboard meta failed');
    }
  }

  async getScoreDistribution(assessmentId: number): Promise<ChartData> {
    try {
      const response = await axios.get<ChartData>(
        `${this.basePath}/${assessmentId}/score-distribution`,
      );
      return response.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถโหลดข้อมูลการแจกแจงคะแนนได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Fetch score distribution failed');
    }
  }

  async getQuestionResponses(
    assessmentId: number,
    params: DataParams,
  ): Promise<DataResponse<QuestionResponseData>> {
    try {
      const response = await axios.get<DataResponse<QuestionResponseData>>(
        `${this.basePath}/${assessmentId}/questions`,
        { params },
      );
      return response.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถโหลดคำตอบของคำถามได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Fetch question responses failed');
    }
  }

  async getParticipants(
    assessmentId: number,
    params: DataParams,
  ): Promise<DataResponse<ParticipantData>> {
    try {
      const response = await axios.get<DataResponse<ParticipantData>>(
        `${this.basePath}/${assessmentId}/participants`,
        { params },
      );
      return response.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถโหลดรายชื่อผู้เข้าสอบได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Fetch participants failed');
    }
  }

  async getParticipantDetails(
    participantId: number,
    params?: Partial<DataParams>,
  ): Promise<DataResponse<ParticipantDetails>> {
    try {
      const response = await axios.get<DataResponse<ParticipantDetails>>(
        `${this.basePath}/participant/${participantId}`,
        { params },
      );
      return response.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถโหลดข้อมูลผู้เข้าสอบได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Fetch participant details failed');
    }
  }
}

export const quizDashboardService = new QuizDashboardService();
