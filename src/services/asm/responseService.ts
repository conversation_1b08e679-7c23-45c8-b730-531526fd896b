import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Response } from 'src/types/models';
import type { ChartData } from 'src/types/chart';
import { isAxiosError } from 'axios';

export class ResponsesService {
  private path = '/responses';

  async create(params: Response): Promise<Response> {
    try {
      const res = await api.post<Response>(this.path, params);
      return res.data;
    } catch {
      Notify.create({ message: 'ส่งคำตอบล้มเหลว', type: 'negative' });
      throw new Error('Create response failed');
    }
  }

  async findAll(): Promise<Response[]> {
    const res = await api.get<Response[]>(this.path);
    return res.data;
  }

  async findOne(id: number): Promise<Response> {
    const res = await api.get<Response>(`${this.path}/${id}`);
    return res.data;
  }

  async findAnswer(submissionId: number, questionId: number): Promise<{ data?: Response }> {
    try {
      return await api.get<Response>(`${this.path}/${submissionId}/${questionId}`);
    } catch (error: unknown) {
      console.warn('findAnswer fallback to empty object due to error:', error);
      return {}; // ส่งว่างๆ กลับแทนการ throw
    }
  }

  async findAnswers(submissionId: number, questionId: number): Promise<Response[]> {
    const res = await api.get<Response[]>(`${this.path}/checkbox/${submissionId}/${questionId}`);
    return res.data; // ✅ ถูกต้อง เพราะ data เป็น array ตรง ๆ
  }

  async findRemoveCheckBoxAnswer(
    submissionId: number,
    questionId: number,
    selectedOptionId: number,
  ): Promise<Response | null> {
    try {
      const res = await api.get<Response>(
        `${this.path}/${submissionId}/${questionId}/${selectedOptionId}`,
      );
      return res.data;
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response?.status === 400) {
        return null;
      }

      console.error('Unexpected error:', error);
      throw error; // หรือ return null หากต้องการกลืน error
    }
  }

  async update(id: number, params: Response): Promise<Response> {
    try {
      const res = await api.patch<Response>(`${this.path}/${id}`, params);
      Notify.create({ message: 'อัปเดตคำตอบเรียบร้อย', type: 'positive' });
      return res.data;
    } catch {
      Notify.create({ message: 'อัปเดตคำตอบล้มเหลว', type: 'negative' });
      throw new Error('Update response failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
      Notify.create({ message: 'ลบคำตอบเรียบร้อย', type: 'positive' });
    } catch {
      Notify.create({ message: 'ลบคำตอบล้มเหลว', type: 'negative' });
      throw new Error('Remove response failed');
    }
  }

  async getChartData(assessmentId: number): Promise<ChartData[]> {
    const res = await api.get<ChartData[]>(`${this.path}/chart-data/${assessmentId}`);
    return res.data;
  }

  async saveUserQuizResponse(data: Response): Promise<Response> {
    try {
      const res = await api.post<Response>(`${this.path}/quiz/save-response`, data);
      return res.data;
    } catch {
      Notify.create({ message: 'ส่งคำตอบไม่สำเร็จ', type: 'negative' });
      throw new Error('Submit quiz response failed');
    }
  }

  async getNumberOfResponses(assessmentId: number): Promise<number> {
    const res = await api.get<{ number: number }>(
      `/assessments${this.path}/header/${assessmentId}`,
    );
    return res.data.number;
  }

  // evaluate responseService
  async getResponseById(id: number): Promise<{ data: ChartData[] }> {
    try {
      // Use the same endpoint pattern as AssessmentService
      return await api.get(`assessments/dashboard/evaluate/${id}`);
    } catch (error) {
      showError('ไม่สามารถดึงข้อมูลแบบประเมินได้');
      throw error;
    }
  }

  async getResponseHeaderById(id: number) {
    try {
      // Use the same endpoint pattern as AssessmentService
      return await api.get(`assessments/header/${id}`);
    } catch (error) {
      showError('ไม่สามารถดึงข้อมูลหัวข้อคำตอบได้');
      throw error;
    }
  }

  async getAssessmentToExcelById(id: number): Promise<void> {
  try {
    console.log('🚀 getAssessmentToExcelById called with:', id);

    // เรียก API โดยตั้งค่า responseType เป็น blob
    const response = await api.get(`assessments/${id}/export/excel`, {
      responseType: 'blob', // บอก axios ว่า response เป็น binary data
    });

    // สร้าง URL จาก Blob
    const url = window.URL.createObjectURL(new Blob([response.data]));

    // สร้าง element <a> เพื่อ trigger การดาวน์โหลด
    const link = document.createElement('a');
    link.href = url;

    // ดึงชื่อไฟล์จาก Content-Disposition header (ถ้ามี)
    const contentDisposition = response.headers['content-disposition'];
    let fileName = `assessment_${id}.xlsx`; // ชื่อไฟล์ default
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)$/);
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = decodeURIComponent(fileNameMatch[1]); // ถอดรหัสชื่อไฟล์
      }
    }
    link.setAttribute('download', fileName);

    // เพิ่มลิงก์ลงใน DOM และ trigger การดาวน์โหลด
    document.body.appendChild(link);
    link.click();

    // ลบลิงก์และ URL ออกจากหน่วยความจำ
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

  } catch (error) {
    showError('ไม่สามารถดึงข้อมูลหัวข้อคำตอบได้');
    console.error('Error downloading Excel:', error);
    throw error;
  }
}
}

// evaluate responseService
const showError = (message: string) => {
  Notify.create({
    message: message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};
