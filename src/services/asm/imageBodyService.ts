import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { ImageBody } from 'src/types/models'; // ปรับ path ตามโปรเจกต์คุณ

export class ImageBodyService {
  private path = 'image-bodies';

  async createImageBody(params: Partial<ImageBody>, file: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await api.post<ImageBody>(`${this.path}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Notify.create({ message: 'เพิ่มรูปภาพเรียบร้อยแล้ว', type: 'positive' });
      return response.data;
    } catch {
      Notify.create({ message: 'เพิ่มรูปภาพล้มเหลว', type: 'negative' });
      throw new Error('Create image body failed');
    }
  }

  async getAllImageBodies(): Promise<ImageBody[]> {
    try {
      const response = await api.get<ImageBody[]>(`${this.path}`);
      return response.data;
    } catch {
      throw new Error('Fetch image bodies failed');
    }
  }

  async getImageBodyById(id: number): Promise<ImageBody> {
    try {
      const response = await api.get<ImageBody>(`${this.path}/${id}`);
      return response.data;
    } catch {
      throw new Error('Fetch image body failed');
    }
  }

  async getImageBodyByItemBlockId(itemBlockId: number): Promise<ImageBody | null> {
    try {
      // Get all image bodies and find the one with matching itemBlockId
      const response = await api.get<ImageBody[]>(`${this.path}`);
      const imageBody = response.data.find((body) => body.itemBlockId === itemBlockId);
      return imageBody || null;
    } catch {
      throw new Error('Fetch image body by itemBlockId failed');
    }
  }

  async updateImageBody(id: number, params: Partial<ImageBody>, file?: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await api.patch<ImageBody>(`${this.path}/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Notify.create({ message: 'อัปเดตรูปภาพเรียบร้อยแล้ว', type: 'positive' });
      return response.data;
    } catch {
      Notify.create({ message: 'อัปเดตรูปภาพล้มเหลว', type: 'negative' });
      throw new Error('Update image body failed');
    }
  }

  async updateImageTextOnly(
    id: number,
    imageText: string,
    existingImagePath?: string,
    existingImageWidth?: number,
    existingImageHeight?: number,
  ): Promise<ImageBody> {
    try {
      console.log('🔧 updateImageTextOnly called with:', {
        id,
        imageText,
        existingImagePath,
        existingImageWidth,
        existingImageHeight,
      });

      // Extract relative path from signed URL if needed
      const extractRelativePath = (path: string | null | undefined): string | null => {
        if (!path) return null;

        // If it's already a relative path, return as is
        if (path.startsWith('uploaded_files/') && !path.includes('?') && !path.includes('http')) {
          return path;
        }

        // If it's a signed URL, extract the relative path part
        if (path.includes('uploaded_files/')) {
          const match = path.match(/uploaded_files\/[^?]+/);
          return match ? match[0] : null;
        }

        return path;
      };

      // CRITICAL: Always send the relative path format to prevent backend from converting to signed URL
      const relativeImagePath = extractRelativePath(existingImagePath);

      console.log('🔄 Path processing for text-only update:', {
        originalPath: existingImagePath,
        extractedRelativePath: relativeImagePath,
        willSendPath: relativeImagePath || '',
      });

      const formData = new FormData();
      formData.append('imageText', imageText);

      // CRITICAL: Always send the relative imagePath to prevent the backend from setting it to null
      // The backend ALWAYS sets imageBody.imagePath = newImagePath, so we must send the current relative path
      formData.append('imagePath', relativeImagePath || '');

      // CRITICAL: Preserve existing image dimensions to prevent them from being reset to 0
      if (existingImageWidth !== undefined && existingImageWidth !== null) {
        formData.append('imageWidth', JSON.stringify(existingImageWidth));
      }
      if (existingImageHeight !== undefined && existingImageHeight !== null) {
        formData.append('imageHeight', JSON.stringify(existingImageHeight));
      }

      const response = await api.patch<ImageBody>(`${this.path}/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      console.log('✅ updateImageTextOnly response:', {
        responseImagePath: response.data.imagePath,
        responseImageText: response.data.imageText,
        sentRelativePath: relativeImagePath,
        pathPreserved: response.data.imagePath === relativeImagePath,
      });

      Notify.create({ message: 'อัปเดตข้อความเรียบร้อยแล้ว', type: 'positive' });
      return response.data;
    } catch (error) {
      console.error('❌ updateImageTextOnly failed:', error);
      Notify.create({ message: 'อัปเดตข้อความล้มเหลว', type: 'negative' });
      throw new Error('Update image text failed');
    }
  }

  async removeImageBody(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
      Notify.create({ message: 'ลบรูปภาพเรียบร้อยแล้ว', type: 'positive' });
    } catch {
      Notify.create({ message: 'ลบรูปภาพล้มเหลว', type: 'negative' });
      throw new Error('Remove image body failed');
    }
  }

  //   private toFormData(data: Record<string, unknown>, file?: File): FormData {
  //     const formData = new FormData();

  //     Object.entries(data).forEach(([key, value]) => {
  //       if (value !== undefined && value !== null) {
  //         if (typeof value === 'object' && value !== null && !(value instanceof File)) {
  //           formData.append(key, JSON.stringify(value));
  //         } else if (
  //           typeof value === 'string' ||
  //           typeof value === 'number' ||
  //           typeof value === 'boolean'
  //         ) {
  //           formData.append(key, String(value));
  //         } else {
  //           formData.append(key, JSON.stringify(value));
  //         }
  //       }
  //     });

  //     if (file) {
  //       formData.append('file', file);
  //     }

  //     return formData;
  //   }
  // }
  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (['imageWidth', 'imageHeight', 'itemBlockId'].includes(key)) {
          formData.append(key, JSON.stringify(value));
        } else if (key === 'imageText') {
          // Send imageText as plain string, not JSON.stringify
          formData.append('imageText', value as string);
        } else if (key === 'imagePath' && typeof value === 'string') {
          // CRITICAL: Handle imagePath as string (for preserving existing paths)
          formData.append('imagePath', value);
        }
      }
    });

    if (file) {
      formData.append('imagePath', file); // ✅ ใช้ key 'imagePath' ตาม API
    }

    return formData;
  }
}
