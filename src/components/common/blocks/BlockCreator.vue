<template>
  <q-page>
    <draggable
      v-model="draggableBlocks"
      :component-data="{
        tag: 'div',
        type: 'transition-group',
        name: !blockCreatorStore.isDragging ? 'flip-list' : null,
      }"
      item-key="id"
      handle=".three-dot-menu"
      :animation="200"
      ghost-class="ghost"
      chosen-class="chosen"
      drag-class="drag"
      @start="onDragStart"
      @end="onDragEnd"
      @change="onDragChange"
    >
      <template #item="{ element: block, index }">
        <div
          :key="block.id"
          :ref="(el) => (blockCreatorUIStore.blockRefs[block.id] = el)"
          class="row justify-center draggable-item"
          :class="{ 'is-dragging': blockCreatorStore.isDragging }"
        >
          <div class="col-auto">
            <div
              v-if="blockCreatorStore.isSectionBlock(index) && blockCreatorStore.totalSections > 1"
              class="col-12 section-container"
            >
              <div class="section-tab">
                ส่วนที่ {{ blockCreatorStore.getSectionNumber(index) }} จาก
                {{ blockCreatorStore.totalSections }}
              </div>
            </div>
            <div class="block-container">
              <div class="block-content full-width">
                <template v-if="block.type === 'HEADER'">
                  <HeaderBlock
                    :itemBlock="block"
                    :index="index"
                    :type="props.type"
                    class="evaluate-item"
                    :class="{
                      'no-top-left-radius':
                        blockCreatorStore.isSectionBlock(index) &&
                        blockCreatorStore.totalSections > 1,
                    }"
                    @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                    @duplicate="handleDuplicateHeaderBlock"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else-if="block.type === 'IMAGE'">
                  <ImageBlock
                    :item-block="block"
                    class="evaluate-item"
                    @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                    @duplicate="handleDuplicateBlock"
                    @delete="() => onClickDeleteBlock(block, index)"
                    @update:image="handleImageUpdate"
                  />
                </template>

                <template v-else>
                  <ItemBlockProvider :block-id="block.id" :item-block="block">
                    <!-- <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      class="evaluate-item"
                      @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                      @duplicate="handleDuplicateBlock"
                      @delete="() => onClickDeleteBlock(block, index)"
                      @update:question="blockCreatorUIStore.handleQuestionUpdate"
                      @update:option="blockCreatorUIStore.handleOptionUpdate"
                      @update:is-required="blockCreatorUIStore.handleIsRequiredUpdate"
                    /> -->
                    <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      class="evaluate-item"
                      @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                      @duplicate="handleDuplicateBlock"
                      @delete="() => onClickDeleteBlock(block, index)"
                      @update:question="handleUpdateQuestion"
                      @update:option="blockCreatorUIStore.handleOptionUpdate"
                      @update:is-required="blockCreatorUIStore.handleIsRequiredUpdate"
                    />
                  </ItemBlockProvider>
                </template>
              </div>
            </div>
          </div>

          <div class="col-auto fixed-fab-col">
            <FloatActionBtnForBlock
              v-show="
                blockCreatorStore.selectedBlockId === `block-${block.id}` &&
                !blockCreatorStore.isDragging
              "
              :disabled="blockCreatorStore.isCreatingBlock"
              :type="props.type"
              @add="
                () => blockCreatorStore.handleAddBlockAfter(index, props.type, props.assessmentId)
              "
              @add-text="
                () => blockCreatorStore.handleAddHeaderAfter(index, props.type, props.assessmentId)
              "
              @add-section="handleAddSection"
              @add-image="(payload) => handleAddImageBlock(index, payload)"
              @image-uploaded="handleImageUploaded"
            />
          </div>
        </div>
      </template>
    </draggable>
  </q-page>
</template>

<script setup lang="ts">
import { watch, nextTick, onMounted, onUnmounted, computed, ref } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import { useBlockCreatorUIStore } from 'src/stores/block_creator_ui';
import type { ItemBlock } from 'src/types/models';
import HeaderBlock from './HeaderBlock.vue';
import ItemBlockComponent from './ItemBlockComponent.vue';
import FloatActionBtnForBlock from './FloatActionBtnForBlock.vue';
import ItemBlockProvider from './ItemBlockProvider.vue';
import ImageBlock from './ImageBlock.vue';
import { defaultBlocks } from 'src/data/defaultBlocks';
import draggable from 'vuedraggable';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';
import { copyBlockContentWithBackendPersistence, getCurrentSection } from 'src/utils/block_helper';
import { ItemBlockService } from 'src/services/asm/itemBlockService';

const props = defineProps<{
  blocks: ItemBlock[];
  type: 'quiz' | 'evaluate';
  assessmentId?: number | null;
}>();

defineOptions({
  name: 'block-creator',
});

// Store instances
const blockCreatorStore = useBlockCreatorStore();
const blockCreatorUIStore = useBlockCreatorUIStore();
const globalStore = useGlobalStore();

// State variables
const isDragging = ref(false);
const blockCreationInProgress = ref(false);
const targetBlockId = ref<number | null>(null);
const fabPositionLock = ref(false);
const isCreatingBlock = ref(false);
let scrollTimeout: NodeJS.Timeout | null = null;

// Memoized assessment service - only recreate when type changes
const assessmentService = computed(() => new AssessmentService(props.type));

// Draggable blocks computed property
const draggableBlocks = computed({
  get: () => blockCreatorStore.blocks.slice().sort((a, b) => a.sequence - b.sequence),
  set: (newBlocks: ItemBlock[]) => {
    blockCreatorStore.updateBlocksOrder(newBlocks);
  },
});

// Function to initialize blocks based on current data
const initializeBlocksFromData = async () => {
  let blocksToUse: ItemBlock[] = [];

  if (props.type === 'evaluate') {
    // For evaluate type, get blocks from the block creator store
    const assessmentBlocks = blockCreatorStore.currentAssessment?.itemBlocks;
    if (assessmentBlocks && assessmentBlocks.length > 0) {
      blocksToUse = assessmentBlocks;
    } else if (props.blocks && props.blocks.length > 0) {
      blocksToUse = props.blocks;
    } else {
      // Only use default blocks as last resort for evaluate type
      blocksToUse = defaultBlocks;
    }
  } else {
    // For quiz type, use props or default blocks
    blocksToUse = props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks;
  }

  blockCreatorStore.initializeBlocks(blocksToUse);

  // Initialize with first block selected ONLY if FAB is not locked (e.g., during image upload)
  if (blockCreatorStore.blocks.length > 0 && !blockCreatorStore.fabPositionLock) {
    blockCreatorStore.selectedBlockId = `block-${blockCreatorStore.blocks[0]!.id}`;
    await nextTick();
    blockCreatorStore.scrollToTarget();
  }
};
type UpdateQuestionPayload = {
  action: 'refresh-block';
  itemBlockId: number;
};

const itemBlockService = new ItemBlockService();

const handleUpdateQuestion = async (payload: UpdateQuestionPayload) => {
  if (payload.action === 'refresh-block') {
    const blockIndex = blockCreatorStore.blocks.findIndex((b) => b.id === payload.itemBlockId);
    if (blockIndex !== -1) {
      const latestBlock = await itemBlockService.getOne(payload.itemBlockId);
      if (latestBlock) {
        // 🔁 แทนที่ด้วย object ใหม่
        const updatedBlock = { ...latestBlock };
        blockCreatorStore.updateBlock(updatedBlock, blockIndex);
      }
    }
  }
};

onMounted(async () => {
  await initializeBlocksFromData();
});

// Cleanup all timeouts and intervals on unmount
onUnmounted(() => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
    scrollTimeout = null;
  }
  // Clear block refs cache
  blockCreatorUIStore.blockRefsCache.clear();
});

// Watch for changes in the block creator store's current assessment
// This ensures that when data is fetched from backend, the blocks are re-initialized
watch(
  () => blockCreatorStore.currentAssessment,
  async (newAssessment) => {
    if (props.type === 'evaluate' && newAssessment?.itemBlocks) {
      await initializeBlocksFromData();
    }
  },
  { deep: true, immediate: false },
);

// Watch for changes in globalIsRequired to update all itemBlocks reactively
watch(
  () => blockCreatorStore.currentAssessment?.globalIsRequired,
  (newGlobalIsRequired, oldGlobalIsRequired) => {
    console.log('🔍 BlockCreator watcher triggered:', {
      type: props.type,
      newValue: newGlobalIsRequired,
      oldValue: oldGlobalIsRequired,
      hasAssessment: !!blockCreatorStore.currentAssessment,
      blocksCount: blockCreatorStore.blocks.length,
    });

    if (props.type === 'evaluate' && newGlobalIsRequired !== undefined) {
      // Update all non-header and non-image blocks in the local store using proper store methods
      let updatedCount = 0;
      blockCreatorStore.blocks.forEach((block, index) => {
        if (block.type !== 'HEADER' && block.type !== 'IMAGE') {
          const currentValue = block.isRequired ?? false;
          console.log(
            `📝 Updating block ${block.id} (index ${index}) isRequired: ${currentValue} → ${newGlobalIsRequired}`,
          );

          // Create updated block object with proper boolean value
          const updatedBlock = {
            ...block,
            isRequired: Boolean(newGlobalIsRequired),
          };

          // Use store's updateBlock method to ensure reactivity
          blockCreatorStore.updateBlock(updatedBlock, index);
          updatedCount++;
        }
      });

      console.log(
        `✅ Updated ${updatedCount} blocks in local store to match global isRequired setting`,
      );
    }
  },
  { immediate: false },
);

// TODO: Move to store - temporary placeholder
const handleAddSection = () => console.log('handleAddSection - to be moved to store');

// TODO: Move to store - temporary placeholder
const handleDuplicateHeaderBlock = () =>
  console.log('handleDuplicateHeaderBlock - to be moved to store');

// Handle ItemBlock duplication with backend persistence
const handleDuplicateBlock = async () => {
  // Get the currently selected block
  const selectedBlockIdValue = blockCreatorStore.selectedBlockId;
  if (!selectedBlockIdValue) {
    console.error('❌ No block selected for duplication');
    return;
  }

  // Extract block ID from selectedBlockId (format: "block-123")
  const blockId = Number(selectedBlockIdValue.split('-')[1]);
  if (!blockId || isNaN(blockId)) {
    console.error('❌ Invalid block ID for duplication:', selectedBlockIdValue);
    return;
  }

  // Find the source block in the store
  const sourceBlockIndex = blockCreatorStore.blocks.findIndex((block) => block.id === blockId);
  if (sourceBlockIndex === -1) {
    console.error('❌ Source block not found for duplication:', blockId);
    return;
  }

  const sourceBlock = blockCreatorStore.blocks[sourceBlockIndex];
  if (!sourceBlock) {
    console.error('❌ Source block is undefined:', blockId);
    return;
  }

  // Prevent duplication during other operations
  if (isCreatingBlock.value) {
    console.warn('⚠️ Block creation already in progress, skipping duplication');
    return;
  }

  try {
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Duplicating block...');

    console.log('📋 Starting block duplication process:', {
      sourceBlockId: sourceBlock.id,
      sourceBlockType: sourceBlock.type,
      sourceBlockIndex: sourceBlockIndex,
      sourceBlockSequence: sourceBlock.sequence,
    });

    // Get assessment ID
    const assessmentId = props.assessmentId || blockCreatorStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      console.error('❌ Assessment ID is required for block duplication');
      return;
    }

    // STEP 1: Prepare sequence updates for existing blocks
    // All blocks after the source block need their sequence incremented by 1
    const blocksToUpdate: ItemBlock[] = [];
    const updatedBlocks = blockCreatorStore.blocks.map((block, index) => {
      if (index > sourceBlockIndex) {
        // Increment sequence for blocks after the source block
        const updatedBlock = { ...block, sequence: block.sequence + 1 };
        blocksToUpdate.push(updatedBlock);
        return updatedBlock;
      }
      return block;
    });

    console.log('🔄 Blocks that need sequence updates:', {
      blocksToUpdateCount: blocksToUpdate.length,
      blockIds: blocksToUpdate.map((b) => b.id),
      oldSequences: blockCreatorStore.blocks.slice(sourceBlockIndex + 1).map((b) => b.sequence),
      newSequences: blocksToUpdate.map((b) => b.sequence),
    });

    // STEP 2: Update sequences in backend if there are blocks to update
    if (blocksToUpdate.length > 0 && props.type === 'evaluate') {
      console.log('🌐 Updating sequences in backend for existing blocks...');
      try {
        const sequenceUpdateResult =
          await assessmentService.value.updateBlockSequences(blocksToUpdate);
        if (!sequenceUpdateResult?.success) {
          globalStore.completeSaveOperation(false, 'Failed to update block sequences');
          console.error('❌ Failed to update block sequences in backend');
          return;
        }
        console.log('✅ Successfully updated sequences in backend');
      } catch (sequenceError) {
        globalStore.completeSaveOperation(false, 'Failed to update block sequences');
        console.error('❌ Error updating block sequences:', sequenceError);
        return;
      }
    }

    // STEP 3: Update local store with new sequences
    blockCreatorStore.blocks.splice(0, blockCreatorStore.blocks.length, ...updatedBlocks);

    // Get current section for the new block
    const currentSection = getCurrentSection(blockCreatorStore.blocks, sourceBlockIndex);

    // STEP 4: Prepare data for the new block with correct sequence
    const newBlockSequence = sourceBlock.sequence + 1;
    const newBlockData = {
      assessmentId: assessmentId,
      sequence: newBlockSequence, // Insert immediately after the source block
      section: currentSection,
      type: sourceBlock.type,
      isRequired: sourceBlock.isRequired ?? false,
    };

    console.log('🔄 Creating new block with data:', newBlockData);

    // STEP 5: Create the new block via backend API
    const createdBlock = await assessmentService.value.createBlock(newBlockData);

    if (!createdBlock) {
      globalStore.completeSaveOperation(false, 'Failed to create duplicate block');
      console.error('❌ Failed to create duplicate block - no response from backend');
      return;
    }

    console.log('✅ New block created successfully:', {
      newBlockId: createdBlock.id,
      newBlockType: createdBlock.type,
      newBlockSequence: createdBlock.sequence,
    });

    // STEP 6: Copy content from source to target using the comprehensive helper function
    console.log('📋 Starting content copy from source to target...');
    let updatedBlock;
    try {
      updatedBlock = await copyBlockContentWithBackendPersistence(sourceBlock, createdBlock);
    } catch (copyError) {
      globalStore.completeSaveOperation(false, 'Failed to copy block content');
      console.error('❌ Error copying block content:', copyError);
      return;
    }

    console.log('✅ Content copy completed:', {
      finalOptionsCount: updatedBlock.options?.length || 0,
      finalQuestionsCount: updatedBlock.questions?.length || 0,
    });

    // STEP 7: Insert the duplicated block immediately after the source block
    const insertIndex = sourceBlockIndex + 1;
    blockCreatorStore.addBlock(updatedBlock, insertIndex);

    // STEP 8: Update the current assessment if this is an evaluate type
    if (props.type === 'evaluate' && blockCreatorStore.currentAssessment) {
      const currentBlocks = blockCreatorStore.currentAssessment.itemBlocks || [];
      // Insert at correct position (index + 1)
      const newAssessmentBlocks = [
        ...currentBlocks.slice(0, insertIndex),
        updatedBlock,
        ...currentBlocks.slice(insertIndex),
      ];
      // Ensure all sequences are properly ordered
      newAssessmentBlocks.forEach((block, idx) => {
        block.sequence = idx + 1;
      });
      blockCreatorStore.currentAssessment.itemBlocks = newAssessmentBlocks;
    }

    // CRITICAL: Set target block ID to allow only this block to receive focus
    targetBlockId.value = updatedBlock.id;
    // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
    fabPositionLock.value = true;

    // Set FAB position and scroll to the new block after it is added
    await setFabAndScroll(updatedBlock.id);
    blockCreatorStore.selectedBlockId = `block-${updatedBlock.id}`;

    // Complete save operation successfully
    globalStore.completeSaveOperation(true, 'Block duplicated successfully');

    // Release locks after DOM is stable
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      // Final confirmation of FAB position on the duplicated block
      blockCreatorStore.selectedBlockId = `block-${updatedBlock.id}`;
      console.log(`✅ Block duplication completed successfully for block ${updatedBlock.id}`);
    }, 800);
  } catch (error) {
    console.error('❌ Error during block duplication:', error);
    globalStore.completeSaveOperation(false, 'Error duplicating block');
  } finally {
    isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

// Helper function for scrolling to target
const scrollToTarget = () => {
  blockCreatorStore.scrollToTarget();
};

// Helper function for setting FAB position and scrolling
const setFabAndScroll = async (blockId: number) => {
  blockCreatorStore.setFabPosition(blockId, false);
  await nextTick();
  scrollToTarget();
};

const onClickDeleteBlock = async (item: ItemBlock, index: number) => {
  // Enhanced pre-deletion validation
  if (!item.id) {
    console.error('❌ Cannot delete block: Missing item ID');
    return;
  }

  if (!item.assessmentId) {
    console.error('❌ Cannot delete block: Missing assessmentId');
    return;
  }

  // Determine appropriate deletion message based on block type
  const getDeleteMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Deleting header...';
      case 'IMAGE':
        return 'Deleting image...';
      default:
        return 'Deleting question...';
    }
  };

  // Determine appropriate success message based on block type
  const getSuccessMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Header deleted successfully';
      case 'IMAGE':
        return 'Image deleted successfully';
      default:
        return 'Question deleted successfully';
    }
  };

  // Determine appropriate error message based on block type
  const getErrorMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Failed to delete header';
      case 'IMAGE':
        return 'Failed to delete image';
      default:
        return 'Failed to delete question';
    }
  };

  try {
    // Enhanced validation for header blocks
    if (item.type === 'HEADER' && !item.headerBody) {
      return;
    }

    // Enhanced validation using block creator store
    if (props.type === 'evaluate') {
      const deletionValidation = blockCreatorStore.validateBlockDeletion(item.id);

      if (!deletionValidation.canDelete) {
        return;
      }
    }

    // Start save operation indicator with appropriate message
    globalStore.startSaveOperation(getDeleteMessage(item.type));

    console.log(`🗑️ Starting deletion of ${item.type} block (ID: ${item.id})`);

    // Delete the ItemBlock (this will cascade delete related entities)
    const deletedBlock = await assessmentService.value.deleteBlock(item);

    if (deletedBlock !== undefined) {
      // Complete save operation successfully
      globalStore.completeSaveOperation(true, getSuccessMessage(item.type));

      console.log(`✅ Successfully deleted ${item.type} block (ID: ${item.id})`);

      // Perform UI cleanup
      await handleBlockDeletionCleanup(item, index);
    } else {
      // Handle case where deletion didn't return expected result
      globalStore.completeSaveOperation(false, getErrorMessage(item.type));
      console.error(
        `❌ Deletion failed for ${item.type} block (ID: ${item.id}) - no response from backend`,
      );
    }
  } catch (error) {
    // Complete save operation with error
    globalStore.completeSaveOperation(false, getErrorMessage(item.type));
    console.error(`❌ Error deleting ${item.type} block (ID: ${item.id}):`, error);
  }
};

// Separate function to handle UI cleanup after successful deletion
const handleBlockDeletionCleanup = async (item: ItemBlock, index: number) => {
  // Remove from local store
  blockCreatorStore.deleteBlock(index);

  // Update the evaluate form store if this is an evaluate type
  if (props.type === 'evaluate' && blockCreatorStore.currentAssessment) {
    const beforeFilter = blockCreatorStore.currentAssessment.itemBlocks || [];

    // Remove the block from the current assessment in the store
    blockCreatorStore.currentAssessment.itemBlocks = beforeFilter.filter(
      (block) => block.id !== item.id,
    );
  }

  // Handle focus after deletion - go to previous block (ItemBlock or HeaderBlock)
  if (blockCreatorStore.blocks.length > 0) {
    let targetIndex;

    // Try to go to the previous block (index - 1)
    if (index > 0) {
      targetIndex = index - 1;
    }
    // If we're deleting the first block, go to the new first block (index 0)
    else {
      targetIndex = 0;
    }

    // Make sure the target index is within bounds
    targetIndex = Math.min(targetIndex, blockCreatorStore.blocks.length - 1);

    const targetBlock = blockCreatorStore.blocks[targetIndex];
    if (targetBlock) {
      console.log(
        `📍 After deletion, moving FAB to previous block at index ${targetIndex} (ID: ${targetBlock.id}, Type: ${targetBlock.type})`,
      );

      // Use the aggressive positioning approach for deletion as well
      blockCreationInProgress.value = true;
      targetBlockId.value = targetBlock.id;
      fabPositionLock.value = true;

      blockCreatorStore.selectedBlockId = `block-${targetBlock.id}`;
      await setFabAndScroll(targetBlock.id);

      // Release locks after positioning
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        console.log(
          `✅ FAB positioned on previous block ${targetBlock.id} (${targetBlock.type}) after deletion`,
        );
      }, 300);
    }
  } else {
    blockCreatorStore.selectedBlockId = undefined;
  }
};

const handleAddImageBlock = async (
  index: number,
  payload: { callback: (id: number | null) => void },
) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    payload.callback(null);
    return;
  }

  try {
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating image block...');

    const assessmentId = props.assessmentId || blockCreatorStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      payload.callback(null);
      return;
    }

    const currentSection = getCurrentSection(blockCreatorStore.blocks, index);

    const newImageData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'IMAGE' as const,
      isRequired: false,
    };

    // Call backend API to create the image block (without image data)
    const addedBlock = await assessmentService.value.createBlock(newImageData);

    if (addedBlock) {
      // CRITICAL: Set target block ID to allow only this block to receive focus
      targetBlockId.value = addedBlock.id;

      // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
      fabPositionLock.value = true;
      blockCreatorStore.selectedBlockId = `block-${addedBlock.id}`;

      // Add to local store with backend response data
      blockCreatorStore.addBlock(addedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && blockCreatorStore.currentAssessment) {
        const currentBlocks = blockCreatorStore.currentAssessment.itemBlocks || [];
        // Insert at correct position (index + 1)
        const newAssessmentBlocks = [
          ...currentBlocks.slice(0, index + 1),
          addedBlock,
          ...currentBlocks.slice(index + 1),
        ];
        // Update sequences for assessment blocks
        newAssessmentBlocks.forEach((block, idx) => {
          block.sequence = idx + 1;
        });
        blockCreatorStore.currentAssessment.itemBlocks = newAssessmentBlocks;
      }

      // Complete save operation successfully
      globalStore.completeSaveOperation(true, 'Image block created successfully');

      // Set FAB position and scroll (FAB is already locked to correct position)
      await setFabAndScroll(addedBlock.id);

      // Release locks after DOM is stable
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        // Final confirmation of FAB position
        blockCreatorStore.selectedBlockId = `block-${addedBlock.id}`;
      }, 800);

      // Return the created block ID to the callback
      payload.callback(addedBlock.id);
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create image block');
      payload.callback(null);
    }
  } catch (error) {
    console.error('❌ Error creating image block:', error);
    globalStore.completeSaveOperation(false, 'Error creating image block');
    payload.callback(null);
  } finally {
    isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

const handleImageUploaded = async () => {
  // This function is called when the image upload is completed
  // We need to refresh the current assessment data to get the updated imageBody
  console.log('✅ Image uploaded successfully, refreshing assessment data...');

  try {
    if (props.type === 'evaluate' && props.assessmentId) {
      // CRITICAL: Preserve the current FAB position before refreshing data
      const currentSelectedBlockId = blockCreatorStore.selectedBlockId;
      const currentBlockId = currentSelectedBlockId
        ? Number(currentSelectedBlockId.split('-')[1])
        : null;

      console.log('🔒 Preserving FAB position during image upload refresh:', {
        currentSelectedBlockId,
        currentBlockId,
      });

      // CRITICAL: Lock FAB position to prevent unwanted changes during refresh
      fabPositionLock.value = true;
      blockCreationInProgress.value = true;
      if (currentBlockId) {
        targetBlockId.value = currentBlockId;
      }

      // Refresh the current assessment to get the updated imageBody data
      const updatedAssessment = await assessmentService.value.fetchOne(props.assessmentId);

      if (updatedAssessment && blockCreatorStore.currentAssessment) {
        // Update the current assessment in the store
        blockCreatorStore.currentAssessment = updatedAssessment;

        // Also update the local blocks array WITHOUT resetting FAB position
        if (updatedAssessment.itemBlocks) {
          blockCreatorStore.initializeBlocks(updatedAssessment.itemBlocks);
        }

        // CRITICAL: Restore FAB position to the ImageBlock that was being worked on
        if (currentSelectedBlockId && currentBlockId) {
          // Verify the block still exists after refresh
          const blockStillExists = updatedAssessment.itemBlocks?.some(
            (block) => block.id === currentBlockId,
          );

          if (blockStillExists) {
            console.log('🎯 Restoring FAB position to ImageBlock:', currentBlockId);
            blockCreatorStore.selectedBlockId = currentSelectedBlockId;

            // Scroll to the restored position
            await nextTick();
            await nextTick();
            scrollToTarget();
          } else {
            console.warn('⚠️ Original block no longer exists, keeping current position');
          }
        }

        console.log('✅ Assessment data refreshed successfully', {
          itemBlocks: updatedAssessment.itemBlocks?.length,
          imageBlocks: updatedAssessment.itemBlocks
            ?.filter((block) => block.type === 'IMAGE')
            .map((block) => ({
              id: block.id,
              imageText: block.imageBody?.imageText,
              imagePath: block.imageBody?.imagePath,
            })),
          restoredFabPosition: currentSelectedBlockId,
        });
      }

      // Release FAB locks after refresh is complete
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        console.log('✅ FAB position preserved successfully after image upload');
      }, 300);
    }
  } catch (error) {
    console.error('❌ Failed to refresh assessment data after image upload:', error);

    // Release locks on error
    fabPositionLock.value = false;
    blockCreationInProgress.value = false;
    targetBlockId.value = null;
  }
};

// Handle image dimension updates from ImageBlock
const handleImageUpdate = (updateData: {
  itemBlockId: number;
  dimensions: { width: number; height: number };
}) => {
  console.log('🎯 Image dimensions updated, updating local data...', {
    itemBlockId: updateData.itemBlockId,
    dimensions: updateData.dimensions,
  });

  try {
    // Find the block in the local store and update dimensions directly
    const blockIndex = blockCreatorStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );

    if (blockIndex !== -1) {
      const block = blockCreatorStore.blocks[blockIndex];
      if (block && block.imageBody) {
        // Update the dimensions in the local block data while preserving all other imageBody properties
        const updatedImageBody = {
          ...block.imageBody,
          imageWidth: updateData.dimensions.width,
          imageHeight: updateData.dimensions.height,
        };

        const updatedBlock = {
          ...block,
          imageBody: updatedImageBody,
        };

        console.log('🔒 Preserving imageBody data during dimension update:', {
          originalImageBody: block.imageBody,
          updatedImageBody: updatedImageBody,
          preservedImagePath: updatedImageBody.imagePath,
          preservedImageText: updatedImageBody.imageText,
        });

        // Update the block in the local store
        blockCreatorStore.updateBlock(updatedBlock, blockIndex);

        // Also update the assessment store if this is an evaluate type
        if (props.type === 'evaluate' && blockCreatorStore.currentAssessment?.itemBlocks) {
          const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
            (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
          );
          if (assessmentBlockIndex !== -1) {
            const assessmentBlock =
              blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
            if (assessmentBlock && assessmentBlock.imageBody) {
              console.log('🔒 Preserving assessment imageBody data during dimension update:', {
                originalAssessmentImageBody: assessmentBlock.imageBody,
                preservedImagePath: assessmentBlock.imageBody.imagePath,
                preservedImageText: assessmentBlock.imageBody.imageText,
              });

              // Update dimensions in the assessment store while preserving all other properties
              assessmentBlock.imageBody.imageWidth = updateData.dimensions.width;
              assessmentBlock.imageBody.imageHeight = updateData.dimensions.height;

              // Trigger reactivity
              blockCreatorStore.currentAssessment.itemBlocks = [
                ...blockCreatorStore.currentAssessment.itemBlocks,
              ];
            }
          }
        }

        console.log('✅ Store image dimensions updated successfully', {
          itemBlockId: updateData.itemBlockId,
          newDimensions: updateData.dimensions,
          updatedBlock: updatedBlock.imageBody,
        });
      } else {
        console.warn('⚠️ ImageBody not found for block:', updateData.itemBlockId);
      }
    } else {
      console.warn('⚠️ Block not found for dimension update:', updateData.itemBlockId);
    }
  } catch (error) {
    console.error('❌ Failed to update local image dimensions:', error);
  }
};

// Drag and drop event handlers
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = () => {
  isDragging.value = false;
};

const onDragChange = async () => {
  // Handle drag change events and sync with backend
  console.log('🔄 Drag change detected, syncing with backend...');

  try {
    // Only sync for evaluate type assessments and when we have blocks
    if (props.type === 'evaluate' && blockCreatorStore.blocks.length > 0) {
      // Start save operation indicator
      globalStore.startSaveOperation('Updating order...');

      // Get current blocks with updated sequences, filtering out invalid blocks
      const validBlocks = blockCreatorStore.blocks.filter((block) => {
        const isValid = block.id && !isNaN(Number(block.id)) && Number(block.id) > 0;
        if (!isValid) {
          console.warn('⚠️ Skipping invalid block in sequence update:', {
            blockId: block.id,
            blockType: block.type,
            isNaN: isNaN(Number(block.id)),
            isPositive: Number(block.id) > 0,
          });
        }
        return isValid;
      });

      if (validBlocks.length === 0) {
        console.warn('⚠️ No valid blocks found for sequence update');
        globalStore.completeSaveOperation(false, 'No valid blocks to update');
        return;
      }

      const blocksToUpdate = validBlocks.map((block, index) => ({
        ...block,
        sequence: index + 1, // Ensure sequence matches current order
      }));

      // Call backend API to update sequences
      const result = await assessmentService.value.updateBlockSequences(blocksToUpdate);

      if (result?.success) {
        // Update the current assessment if this is an evaluate type
        if (blockCreatorStore.currentAssessment) {
          // Update the assessment store with the new order
          blockCreatorStore.currentAssessment.itemBlocks = [...blocksToUpdate];
        }

        // Complete save operation successfully
        globalStore.completeSaveOperation(true, 'Order updated successfully');

        console.log('✅ Block order synchronized with backend successfully');
      } else {
        // Handle case where API call didn't return expected result
        globalStore.completeSaveOperation(false, 'Failed to update order');
        console.error('❌ Block order sync failed - no success response from backend');
      }
    }
  } catch (error) {
    // Complete save operation with error
    globalStore.completeSaveOperation(false, 'Failed to update order');
    console.error('❌ Error syncing block order with backend:', error);
  }
};

// Optimized watcher with debouncing for better performance
watch(
  () => blockCreatorStore.selectedBlockId,
  (newValue) => {
    // During block creation, aggressively enforce the target position
    if (blockCreationInProgress.value && targetBlockId.value) {
      const expectedId = `block-${targetBlockId.value}`;
      if (newValue !== expectedId) {
        console.log(`🔒 WATCHER: Forcing FAB back to ${expectedId} from ${newValue}`);
        // Use nextTick to avoid infinite loops
        void nextTick(() => {
          blockCreatorStore.selectedBlockId = expectedId;
        });
        return;
      }
    }

    // Debounced scroll behavior for better performance
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
    scrollTimeout = setTimeout(() => {
      scrollToTarget();
    }, 16);
  },
  { flush: 'post' }, // Run after DOM updates for better performance
);
</script>

<style scoped>
.fixed-fab-col {
  width: 48px;
}

.section-container {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
  position: relative;
  left: 0;
}

.no-top-left-radius {
  border-top-left-radius: 0 !important;
}

/* Drag and Drop Styles */
.draggable-item {
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.draggable-item.is-dragging {
  opacity: 0.8;
}

.block-container {
  position: relative;
  width: 100%;
}

.block-content {
  flex: 1;
}

/* Drag states */
.ghost {
  opacity: 0.5;
  background: #f0f0f0;
  border: 2px dashed #ccc;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Flip animation for smooth transitions */
.flip-list-move,
.flip-list-enter-active,
.flip-list-leave-active {
  transition: all 0.3s ease;
}

.flip-list-enter-from,
.flip-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.flip-list-leave-active {
  position: absolute;
}
</style>
