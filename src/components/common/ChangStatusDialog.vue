<template>
  <q-dialog v-model="dialog" persistent>
    <q-card class="q-pa-lg q-mx-md" style="border-radius: 16px; width: 350px; max-width: 90vw">
      <!-- Card สถานะ 2 ฝั่ง -->
      <q-card-section>
        <div class="row q-col-gutter-md justify-around">
          <!-- Card TRUE -->
          <q-card
            flat
            bordered
            class="col-6 text-center cursor-pointer"
            :class="assStastatus ? 'bg-primary text-white' : ''"
            @click="assStastatus = true"
          >
            <q-card-section>
              <q-icon name="check_circle" size="24px" />
              <div class="text-subtitle1 q-mt-sm">True</div>
            </q-card-section>
          </q-card>

          <!-- Card FALSE -->
          <q-card
            flat
            bordered
            class="col-6 text-center cursor-pointer"
            :class="!assStastatus ? 'bg-primary text-white' : ''"
            @click="assStastatus = false"
          >
            <q-card-section>
              <q-icon name="cancel" size="24px" />
              <div class="text-subtitle1 q-mt-sm">False</div>
            </q-card-section>
          </q-card>
        </div>
      </q-card-section>

      <!-- QR Code -->
      <div class="flex justify-center q-mb-lg">
        <!-- แสดง QR Code ได้ที่นี่ -->
      </div>

      <!-- ปุ่ม -->
      <div class="row justify-between q-mt-lg">
        <q-btn
          unelevated
          label="ยกเลิก"
          color="secondary"
          class="col-5"
          @click="dialog = false"
          style="width: 130px"
        />
        <q-btn
          unelevated
          :label="confirmLabel"
          color="positive"
          class="col-5"
          @click="confirm"
          style="width: 130px"
        />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import type { AssessmentType } from 'src/types/data';
const dialog = ref(false);
const assId = ref(0);
const assStastatus = ref(false);
const confirmLabel = ref('ยืนยัน');
const path = ref<AssessmentType>('evaluate');
function openDialog(id: number, status: boolean, okStr: string, pathPrefix: string) {
  console.log(pathPrefix);
  assId.value = id;
  assStastatus.value = status;
  confirmLabel.value = okStr;
  path.value = pathPrefix as AssessmentType;
  dialog.value = true;
}

async function confirm() {
  console.log('ยืนยันแล้ว: ', assStastatus.value);
  await new AssessmentService(path.value).updateOne(assId.value, { status: assStastatus.value });
  dialog.value = false;
  window.location.reload();
}

defineExpose({ openDialog });
</script>
